<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Learning Tool - Edit Mode</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: normal;
        }

        .level-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .level-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #34495e;
            background-color: transparent;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: normal;
            position: relative;
        }

        .level-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .level-btn.active {
            border-color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .level-btn[data-level="A"].active {
            background-color: #9b59b6;
            border-color: #9b59b6;
        }

        .level-btn[data-level="B"].active {
            background-color: #f39c12;
            border-color: #f39c12;
        }

        .level-btn[data-level="C"].active {
            background-color: #e74c3c;
            border-color: #e74c3c;
        }

        .level-btn[data-level="C+"].active {
            background-color: #e16711;
            border-color: #e16711;
        }

        .edit-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #27ae60;
            background-color: transparent;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: normal;
            margin-left: 1rem;
        }

        .edit-btn:hover {
            background-color: rgba(39, 174, 96, 0.2);
        }

        .edit-btn.active {
            background-color: #27ae60;
            border-color: #27ae60;
        }

        .main-content {
            display: flex;
            flex: 1;
            gap: 2rem;
            padding: 2rem;
        }

        .text-area {
            flex: 2;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 2rem;
            overflow-y: auto;
            max-height: 70vh;
        }

        .text-content {
            font-size: 1.1rem;
            line-height: 1.8;
            font-weight: normal;
        }

        .text-editor {
            width: 100%;
            height: 400px;
            padding: 1rem;
            border: 2px solid #3498db;
            border-radius: 4px;
            font-size: 1.1rem;
            line-height: 1.8;
            font-family: inherit;
            resize: vertical;
            display: none;
        }

        .text-editor.show {
            display: block;
        }

        .editor-controls {
            margin-bottom: 1rem;
            display: none;
        }

        .editor-controls.show {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #3498db;
            background-color: #3498db;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: normal;
        }

        .editor-btn:hover {
            background-color: #2980b9;
        }

        .editor-btn.secondary {
            background-color: #95a5a6;
            border-color: #95a5a6;
        }

        .editor-btn.secondary:hover {
            background-color: #7f8c8d;
        }

        .sidebar {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .dictionary-section {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
            min-height: 200px;
        }

        .dictionary-section h2 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-weight: normal;
        }

        .dictionary-content {
            font-size: 0.95rem;
        }

        .placeholder {
            color: #999;
            font-style: italic;
            font-weight: normal;
        }

        .controls-section {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .controls-section h3 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-weight: normal;
        }

        .control-group {
            margin-bottom: 0.8rem;
        }

        .control-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: normal;
        }

        .control-group input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .bottom-meanings {
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            padding: 1.5rem 2rem;
            display: none;
        }

        .bottom-meanings.show {
            display: block;
        }

        .bottom-meanings h3 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-weight: normal;
        }

        .meanings-content {
            font-size: 0.95rem;
            font-weight: normal;
        }

        /* Word styling - colors based on difficulty level */
        .word-level-a {
            color: #9b59b6;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-a:hover {
            background-color: rgba(155, 89, 182, 0.1);
        }

        .word-level-b {
            color: #f39c12;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-b:hover {
            background-color: rgba(243, 156, 18, 0.1);
        }

        .word-level-c {
            color: #e74c3c;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-c:hover {
            background-color: rgba(231, 76, 60, 0.1);
        }

        .word-level-cplus {
            color: #e16711;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-cplus:hover {
            background-color: rgba(225, 103, 17, 0.1);
        }

        /* Regular words (sight words) - default black */
        .word-regular {
            color: #333;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-regular:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* When level is disabled, show as black */
        .word-level-a.disabled,
        .word-level-b.disabled,
        .word-level-c.disabled,
        .word-level-cplus.disabled {
            color: #333;
        }

        .word-level-a.disabled:hover,
        .word-level-b.disabled:hover,
        .word-level-c.disabled:hover,
        .word-level-cplus.disabled:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* Phrase styling - using underline and colors */
        .phrase-level-a {
            color: #9b59b6;
            text-decoration: underline;
            text-decoration-color: #9b59b6;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-a:hover {
            background-color: rgba(155, 89, 182, 0.1);
        }

        .phrase-level-b {
            color: #f39c12;
            text-decoration: underline;
            text-decoration-color: #f39c12;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-b:hover {
            background-color: rgba(243, 156, 18, 0.1);
        }

        .phrase-level-c {
            color: #e74c3c;
            text-decoration: underline;
            text-decoration-color: #e74c3c;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-c:hover {
            background-color: rgba(231, 76, 60, 0.1);
        }

        .phrase-level-cplus {
            color: #e16711;
            text-decoration: underline;
            text-decoration-color: #e16711;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-cplus:hover {
            background-color: rgba(225, 103, 17, 0.1);
        }

        /* When phrase level is disabled */
        .phrase-level-a.disabled,
        .phrase-level-b.disabled,
        .phrase-level-c.disabled,
        .phrase-level-cplus.disabled {
            color: #333;
            text-decoration-color: #333;
        }

        .phrase-level-a.disabled:hover,
        .phrase-level-b.disabled:hover,
        .phrase-level-c.disabled:hover,
        .phrase-level-cplus.disabled:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* Inline meanings */
        .inline-meaning {
            color: #666;
            font-size: 0.9rem;
            font-style: italic;
            margin-left: 0.3rem;
            font-weight: normal;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                padding: 1rem;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .text-area {
                max-height: 50vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header with level controls -->
        <header class="header">
            <h1>English Learning Tool</h1>
            <div class="level-controls">
                <span>显示颜色:</span>
                <button class="level-btn" data-level="A">A级 (220-1000)</button>
                <button class="level-btn" data-level="B">B级 (1000-2000)</button>
                <button class="level-btn" data-level="C">C级 (2000-3000)</button>
                <button class="level-btn" data-level="C+">C+级 (不常用)</button>
                <button class="edit-btn" id="editBtn">编辑模式</button>
            </div>
        </header>

        <!-- Main content area -->
        <main class="main-content">
            <!-- Left side: Text area -->
            <div class="text-area">
                <div class="editor-controls" id="editorControls">
                    <button class="editor-btn" id="processBtn">处理文本</button>
                    <button class="editor-btn secondary" id="cancelBtn">取消</button>
                    <button class="editor-btn secondary" id="clearBtn">清空</button>
                    <span style="color: #666; font-size: 0.9rem;">粘贴英文文本后点击"处理文本"进行自动分级</span>
                </div>
                <textarea class="text-editor" id="textEditor" placeholder="在这里粘贴或输入英文文本..."></textarea>
                <div class="text-content" id="textContent">
                    <!-- Sample text for demonstration -->
                    <p><span class="word-regular" data-word="The">The</span> <span class="word-level-a" data-word="quick">quick</span> <span class="word-regular" data-word="brown">brown</span> <span class="word-regular" data-word="fox">fox</span> <span class="phrase-level-a" data-phrase="jumps over"><span class="word-level-a" data-word="jumps">jumps</span> <span class="word-regular" data-word="over">over</span></span> <span class="word-regular" data-word="the">the</span> <span class="word-regular" data-word="lazy">lazy</span> <span class="word-regular" data-word="dog">dog</span>. <span class="word-regular" data-word="This">This</span> <span class="word-level-b" data-word="sentence">sentence</span> <span class="word-level-b" data-word="contains">contains</span> <span class="word-level-c" data-word="various">various</span> <span class="word-regular" data-word="words">words</span> <span class="word-regular" data-word="of">of</span> <span class="word-level-a" data-word="different">different</span> <span class="word-level-b" data-word="difficulty">difficulty</span> <span class="word-regular" data-word="levels">levels</span>. <span class="word-regular" data-word="Some">Some</span> <span class="phrase-level-c" data-phrase="fixed expressions"><span class="word-regular" data-word="fixed">fixed</span> <span class="word-level-c" data-word="expressions">expressions</span></span> <span class="word-regular" data-word="are">are</span> <span class="word-regular" data-word="also">also</span> <span class="word-level-b" data-word="included">included</span> <span class="word-regular" data-word="to">to</span> <span class="word-level-cplus" data-word="demonstrate">demonstrate</span> <span class="word-regular" data-word="the">the</span> <span class="word-level-cplus" data-word="functionality">functionality</span>.</p>
                    
                    <p><span class="word-level-a" data-word="Learning">Learning</span> <span class="word-regular" data-word="English">English</span> <span class="word-level-b" data-word="requires">requires</span> <span class="word-level-a" data-word="practice">practice</span> <span class="word-regular" data-word="and">and</span> <span class="word-level-b" data-word="dedication">dedication</span>. <span class="word-level-a" data-word="Students">Students</span> <span class="word-level-a" data-word="often">often</span> <span class="phrase-level-c" data-phrase="struggle with"><span class="word-level-c" data-word="struggle">struggle</span> <span class="word-regular" data-word="with">with</span></span> <span class="word-level-c" data-word="understanding">understanding</span> <span class="word-level-cplus" data-word="complex">complex</span> <span class="word-level-c" data-word="vocabulary">vocabulary</span> <span class="word-regular" data-word="and">and</span> <span class="phrase-level-cplus" data-phrase="grammatical structures"><span class="word-level-cplus" data-word="grammatical">grammatical</span> <span class="word-level-cplus" data-word="structures">structures</span></span>.</p>
                </div>
            </div>

            <!-- Right side: Dictionary and controls -->
            <div class="sidebar">
                <!-- Dictionary section -->
                <div class="dictionary-section">
                    <h2>Dictionary</h2>
                    <div class="dictionary-content" id="dictionaryContent">
                        <p class="placeholder">Click on a word to see its definition</p>
                    </div>
                </div>

                <!-- Phrase section -->
                <div class="dictionary-section">
                    <h2>固定搭配区</h2>
                    <div class="dictionary-content" id="phraseContent">
                        <p class="placeholder">Click on a phrase to see its meaning</p>
                    </div>
                </div>

                <!-- Controls section -->
                <div class="controls-section">
                    <h3>Display Options</h3>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showLevelA" checked>
                            Show Level A words meanings inline
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showLevelB">
                            Show Level B words meanings inline
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showLevelC">
                            Show Level C words meanings inline
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showPhrases">
                            Show phrase meanings inline
                        </label>
                    </div>
                </div>
            </div>
        </main>

        <!-- Bottom section for word meanings -->
        <footer class="bottom-meanings" id="bottomMeanings">
            <h3>Word Meanings</h3>
            <div class="meanings-content" id="meaningsContent">
                <!-- Meanings will be populated by JavaScript -->
            </div>
        </footer>
    </div>

    <script>
        // Global variable to store frequency database loaded from JSON
        let frequencyDatabase = null;

        // Function to load vocabulary data from JSON file
        async function loadVocabularyData() {
            try {
                const response = await fetch('./vocabulary.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();

                // Convert arrays to Sets for faster lookup and map to our level system
                frequencyDatabase = {
                    sightWords: new Set(data.A1),      // A1 -> Sight Words (基础词汇)
                    aLevel: new Set(data.A2),          // A2 -> A级 (220-1000)
                    bLevel: new Set(data.A3),          // A3 -> B级 (1000-2000)
                    cLevel: new Set(data.A4)           // A4 -> C级 (2000-3000)
                };

                console.log('Vocabulary data loaded successfully');
                return true;
            } catch (error) {
                console.error('Error loading vocabulary data:', error);
                // Fallback to a minimal dataset if JSON loading fails
                frequencyDatabase = {
                    sightWords: new Set(['the', 'of', 'and', 'to', 'a', 'in', 'is', 'it', 'you', 'that']),
                    aLevel: new Set(['people', 'time', 'person', 'way', 'look']),
                    bLevel: new Set(['education', 'knowledge', 'information']),
                    cLevel: new Set(['demonstrate', 'complex', 'functionality'])
                };
                return false;
            }
        }



        // Common phrases database
        const commonPhrases = {
            'in order to': 'B',
            'as well as': 'A',
            'such as': 'A',
            'due to': 'B',
            'because of': 'A',
            'in spite of': 'B',
            'instead of': 'A',
            'rather than': 'B',
            'more than': 'A',
            'less than': 'A',
            'at least': 'A',
            'at most': 'B',
            'for example': 'A',
            'for instance': 'B',
            'in fact': 'A',
            'in general': 'B',
            'in particular': 'B',
            'on the other hand': 'B',
            'as a result': 'B',
            'in conclusion': 'C',
            'struggle with': 'C',
            'deal with': 'A',
            'cope with': 'B',
            'focus on': 'A',
            'depend on': 'A',
            'rely on': 'B',
            'consist of': 'B',
            'result in': 'B',
            'lead to': 'A',
            'contribute to': 'B',
            'according to': 'B',
            'thanks to': 'A',
            'in terms of': 'C',
            'with regard to': 'C',
            'with respect to': 'C',
            'in relation to': 'C',
            'fixed expressions': 'C',
            'jumps over': 'A',
            'grammatical structures': 'C+'
        };

        // Sample word database with meanings
        const wordDatabase = {
            // A级词汇 (220-1000)
            'quick': { level: 'A', meaning: 'moving fast; happening in a short time' },
            'practice': { level: 'A', meaning: 'repeated exercise to improve skill' },
            'jumps': { level: 'A', meaning: 'moves quickly up and over something' },
            'different': { level: 'A', meaning: 'not the same' },
            'Learning': { level: 'A', meaning: 'the process of acquiring knowledge' },
            'Students': { level: 'A', meaning: 'people who are learning' },
            'often': { level: 'A', meaning: 'frequently' },
            
            // B级词汇 (1000-2000)
            'difficulty': { level: 'B', meaning: 'the state of being hard to do or understand' },
            'dedication': { level: 'B', meaning: 'commitment to a task or purpose' },
            'sentence': { level: 'B', meaning: 'a group of words that express a complete thought' },
            'contains': { level: 'B', meaning: 'has or includes something' },
            'included': { level: 'B', meaning: 'contained as part of a whole' },
            'requires': { level: 'B', meaning: 'needs' },
            
            // C级词汇 (2000-3000)
            'various': { level: 'C', meaning: 'different from one another; diverse' },
            'expressions': { level: 'C', meaning: 'words or phrases that convey meaning' },
            'understanding': { level: 'C', meaning: 'the ability to comprehend something' },
            'vocabulary': { level: 'C', meaning: 'all the words used by a language' },
            'struggle': { level: 'C', meaning: 'make great efforts to achieve something difficult' },
            
            // C+级词汇 (不常用词)
            'demonstrate': { level: 'C+', meaning: 'clearly show the existence or truth of something' },
            'complex': { level: 'C+', meaning: 'consisting of many different parts; complicated' },
            'functionality': { level: 'C+', meaning: 'the quality of being functional or practical' },
            'grammatical': { level: 'C+', meaning: 'relating to grammar' },
            'structures': { level: 'C+', meaning: 'arrangements or organizations' },
            
            // Sight words (220个已知词汇，默认黑色)
            'The': { level: 'Sight', meaning: 'used to refer to specific things' },
            'the': { level: 'Sight', meaning: 'used to refer to specific things' },
            'brown': { level: 'Sight', meaning: 'a color like that of earth or wood' },
            'fox': { level: 'Sight', meaning: 'a wild animal with red fur and a bushy tail' },
            'over': { level: 'Sight', meaning: 'above and across' },
            'lazy': { level: 'Sight', meaning: 'unwilling to work or use energy' },
            'dog': { level: 'Sight', meaning: 'a domestic animal kept as a pet' },
            'This': { level: 'Sight', meaning: 'used to identify a specific thing' },
            'of': { level: 'Sight', meaning: 'expressing the relationship between things' },
            'words': { level: 'Sight', meaning: 'units of language that have meaning' },
            'levels': { level: 'Sight', meaning: 'degrees or stages' },
            'Some': { level: 'Sight', meaning: 'a certain amount or number of' },
            'fixed': { level: 'Sight', meaning: 'not able to be changed' },
            'are': { level: 'Sight', meaning: 'present tense of "be"' },
            'also': { level: 'Sight', meaning: 'in addition' },
            'to': { level: 'Sight', meaning: 'expressing direction or intention' },
            'English': { level: 'Sight', meaning: 'the language spoken in England and many other countries' },
            'and': { level: 'Sight', meaning: 'used to connect words or phrases' },
            'with': { level: 'Sight', meaning: 'accompanied by' }
        };

        const phraseDatabase = {
            'jumps over': { level: 'A', meaning: 'leaps above something' },
            'fixed expressions': { level: 'C', meaning: 'phrases with established meanings' },
            'struggle with': { level: 'C', meaning: 'have difficulty dealing with' },
            'grammatical structures': { level: 'C+', meaning: 'patterns of word arrangement in language' }
        };

        // Function to classify word level based on frequency database
        function classifyWord(word) {
            const lowerWord = word.toLowerCase();

            if (frequencyDatabase.sightWords.has(lowerWord)) {
                return 'Sight';
            } else if (frequencyDatabase.aLevel.has(lowerWord)) {
                return 'A';
            } else if (frequencyDatabase.bLevel.has(lowerWord)) {
                return 'B';
            } else if (frequencyDatabase.cLevel.has(lowerWord)) {
                return 'C';
            } else {
                return 'C+'; // Unknown words default to C+
            }
        }

        // Function to process text and add word classifications
        function processTextContent(text) {
            // Check if frequency database is loaded
            if (!frequencyDatabase) {
                console.error('Frequency database not loaded yet');
                return '<p>Loading vocabulary data...</p>';
            }
            // Split text into sentences
            const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
            let processedHTML = '';

            sentences.forEach((sentence, index) => {
                if (sentence.trim().length === 0) return;

                // Clean and split sentence into words
                const words = sentence.trim().split(/\s+/);
                let processedSentence = '';

                words.forEach((word, wordIndex) => {
                    // Extract punctuation
                    const match = word.match(/^([^\w]*)(.*?)([^\w]*)$/);
                    const prefix = match[1] || '';
                    const cleanWord = match[2] || '';
                    const suffix = match[3] || '';

                    if (cleanWord.length > 0) {
                        const level = classifyWord(cleanWord);
                        const className = level === 'Sight' ? 'word-regular' : `word-level-${level.toLowerCase()}`;

                        processedSentence += `${prefix}<span class="${className}" data-word="${cleanWord}">${cleanWord}</span>${suffix}`;
                    } else {
                        processedSentence += word;
                    }

                    if (wordIndex < words.length - 1) {
                        processedSentence += ' ';
                    }
                });

                processedHTML += `<p>${processedSentence}.</p>\n`;
            });

            return processedHTML;
        }

        // DOM elements
        const textContent = document.getElementById('textContent');
        const textEditor = document.getElementById('textEditor');
        const editorControls = document.getElementById('editorControls');
        const editBtn = document.getElementById('editBtn');
        const processBtn = document.getElementById('processBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const clearBtn = document.getElementById('clearBtn');
        const dictionaryContent = document.getElementById('dictionaryContent');
        const phraseContent = document.getElementById('phraseContent');
        const bottomMeanings = document.getElementById('bottomMeanings');
        const meaningsContent = document.getElementById('meaningsContent');
        const levelButtons = document.querySelectorAll('.level-btn');
        const checkboxes = {
            A: document.getElementById('showLevelA'),
            B: document.getElementById('showLevelB'),
            C: document.getElementById('showLevelC'),
            phrases: document.getElementById('showPhrases')
        };

        let levelStates = {
            'A': false,
            'B': false,
            'C': false,
            'C+': false
        };

        let isEditMode = false;

        // Edit mode functions
        function toggleEditMode() {
            isEditMode = !isEditMode;

            if (isEditMode) {
                editBtn.classList.add('active');
                editBtn.textContent = '退出编辑';
                textContent.style.display = 'none';
                textEditor.classList.add('show');
                editorControls.classList.add('show');

                // Load current content into editor
                const currentText = textContent.textContent || textContent.innerText || '';
                textEditor.value = currentText.replace(/\s+/g, ' ').trim();
            } else {
                editBtn.classList.remove('active');
                editBtn.textContent = '编辑模式';
                textContent.style.display = 'block';
                textEditor.classList.remove('show');
                editorControls.classList.remove('show');
            }
        }

        async function processText() {
            const inputText = textEditor.value.trim();
            if (!inputText) {
                alert('请输入一些文本');
                return;
            }

            // Ensure vocabulary data is loaded
            if (!frequencyDatabase) {
                const loadSuccess = await loadVocabularyData();
                if (!loadSuccess) {
                    alert('无法加载词汇数据，请刷新页面重试');
                    return;
                }
            }

            // Process the text and generate HTML with word classifications
            const processedHTML = processTextContent(inputText);
            textContent.innerHTML = processedHTML;

            // Exit edit mode
            toggleEditMode();

            // Update display
            updateDisplay();
        }

        function cancelEdit() {
            toggleEditMode();
        }

        function clearEditor() {
            textEditor.value = '';
        }

        // Initialize the application
        async function init() {
            // First load vocabulary data from JSON
            const loadSuccess = await loadVocabularyData();
            if (!loadSuccess) {
                console.warn('Failed to load vocabulary data, using fallback dataset');
            }

            addEventListeners();
            updateDisplay();
        }

        function addEventListeners() {
            // Edit mode toggle
            editBtn.addEventListener('click', toggleEditMode);

            // Editor controls
            processBtn.addEventListener('click', processText);
            cancelBtn.addEventListener('click', cancelEdit);
            clearBtn.addEventListener('click', clearEditor);

            // Level button clicks - toggle functionality
            levelButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    if (button === editBtn) return; // Skip edit button

                    const level = button.getAttribute('data-level');
                    levelStates[level] = !levelStates[level];

                    if (levelStates[level]) {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }

                    updateWordColors();
                });
            });

            // Checkbox changes
            Object.values(checkboxes).forEach(checkbox => {
                checkbox.addEventListener('change', updateInlineMeanings);
            });

            // Word and phrase clicks
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('word-level-a') ||
                    e.target.classList.contains('word-level-b') ||
                    e.target.classList.contains('word-level-c') ||
                    e.target.classList.contains('word-level-c-plus') ||
                    e.target.classList.contains('word-regular')) {
                    showWordDefinition(e.target);
                } else if (e.target.classList.contains('phrase-level-a') ||
                           e.target.classList.contains('phrase-level-b') ||
                           e.target.classList.contains('phrase-level-c') ||
                           e.target.classList.contains('phrase-level-c-plus')) {
                    showPhraseDefinition(e.target);
                }
            });
        }

        function updateDisplay() {
            updateWordColors();
            updateInlineMeanings();
        }

        function updateWordColors() {
            // Update word colors based on level states
            ['A', 'B', 'C', 'C+'].forEach(level => {
                const selector = `.word-level-${level.toLowerCase()}`;
                const phraseSelector = `.phrase-level-${level.toLowerCase()}`;

                document.querySelectorAll(selector).forEach(el => {
                    if (levelStates[level]) {
                        el.classList.remove('disabled');
                    } else {
                        el.classList.add('disabled');
                    }
                });

                document.querySelectorAll(phraseSelector).forEach(el => {
                    if (levelStates[level]) {
                        el.classList.remove('disabled');
                    } else {
                        el.classList.add('disabled');
                    }
                });
            });
        }

        function updateInlineMeanings() {
            // Remove existing inline meanings
            document.querySelectorAll('.inline-meaning').forEach(el => el.remove());

            // Add inline meanings based on checkboxes
            if (checkboxes.A.checked) {
                addInlineMeaningsForLevel('A');
            }
            if (checkboxes.B.checked) {
                addInlineMeaningsForLevel('B');
            }
            if (checkboxes.C.checked) {
                addInlineMeaningsForLevel('C');
            }
            if (checkboxes.phrases.checked) {
                addInlineMeaningsForPhrases();
            }
        }

        function addInlineMeaningsForLevel(level) {
            const selector = `.word-level-${level.toLowerCase()}`;
            document.querySelectorAll(selector).forEach(wordEl => {
                const word = wordEl.getAttribute('data-word');
                const wordData = wordDatabase[word];
                if (wordData) {
                    const meaningSpan = document.createElement('span');
                    meaningSpan.className = 'inline-meaning';
                    meaningSpan.textContent = `(${wordData.meaning})`;
                    wordEl.parentNode.insertBefore(meaningSpan, wordEl.nextSibling);
                }
            });
        }

        function addInlineMeaningsForPhrases() {
            document.querySelectorAll('[class*="phrase-level-"]').forEach(phraseEl => {
                const phrase = phraseEl.getAttribute('data-phrase');
                const phraseData = phraseDatabase[phrase];
                if (phraseData) {
                    const meaningSpan = document.createElement('span');
                    meaningSpan.className = 'inline-meaning';
                    meaningSpan.textContent = `(${phraseData.meaning})`;
                    phraseEl.parentNode.insertBefore(meaningSpan, phraseEl.nextSibling);
                }
            });
        }

        function showWordDefinition(wordEl) {
            const word = wordEl.getAttribute('data-word');
            let wordData = wordDatabase[word];

            // If word not found in database, create a basic entry based on classification
            if (!wordData) {
                const level = classifyWord(word);
                wordData = {
                    level: level,
                    meaning: 'Definition not available in database'
                };
            }

            let levelDisplay = wordData.level;
            if (wordData.level === 'Sight') {
                levelDisplay = 'Sight Words (已知词汇)';
            } else {
                levelDisplay = `${wordData.level}级`;
            }

            dictionaryContent.innerHTML = `
                <h4>${word}</h4>
                <p><strong>Level:</strong> ${levelDisplay}</p>
                <p><strong>Meaning:</strong> ${wordData.meaning}</p>
            `;

            // Also show in bottom section
            bottomMeanings.classList.add('show');
            meaningsContent.innerHTML = `
                <h4>${word} (${levelDisplay})</h4>
                <p>${wordData.meaning}</p>
            `;
        }

        function showPhraseDefinition(phraseEl) {
            const phrase = phraseEl.getAttribute('data-phrase');
            const phraseData = phraseDatabase[phrase];

            if (phraseData) {
                phraseContent.innerHTML = `
                    <h4>${phrase}</h4>
                    <p><strong>Level:</strong> ${phraseData.level}级</p>
                    <p><strong>Meaning:</strong> ${phraseData.meaning}</p>
                `;

                // Also show in bottom section
                bottomMeanings.classList.add('show');
                meaningsContent.innerHTML = `
                    <h4>固定搭配: ${phrase} (${phraseData.level}级)</h4>
                    <p>${phraseData.meaning}</p>
                `;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>