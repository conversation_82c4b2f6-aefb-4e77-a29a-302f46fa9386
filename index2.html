<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>English Learning Tool - Edit Mode</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: #2c3e50;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: normal;
        }

        .level-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .level-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #34495e;
            background-color: transparent;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: normal;
            position: relative;
        }

        .level-btn:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .level-btn.active {
            border-color: white;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .level-btn[data-level="A"].active {
            background-color: #9b59b6;
            border-color: #9b59b6;
        }

        .level-btn[data-level="B"].active {
            background-color: #f39c12;
            border-color: #f39c12;
        }

        .level-btn[data-level="C"].active {
            background-color: #e74c3c;
            border-color: #e74c3c;
        }

        .level-btn[data-level="C+"].active {
            background-color: #e16711;
            border-color: #e16711;
        }

        .edit-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #27ae60;
            background-color: transparent;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: normal;
            margin-left: 1rem;
        }

        .edit-btn:hover {
            background-color: rgba(39, 174, 96, 0.2);
        }

        .edit-btn.active {
            background-color: #27ae60;
            border-color: #27ae60;
        }

        .main-content {
            display: flex;
            flex: 1;
            gap: 2rem;
            padding: 2rem;
        }

        .text-area {
            flex: 2;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 2rem;
            overflow-y: auto;
            max-height: 70vh;
        }

        .text-content {
            font-size: 1.1rem;
            line-height: 1.8;
            font-weight: normal;
        }

        .text-editor {
            width: 100%;
            height: 400px;
            padding: 1rem;
            border: 2px solid #3498db;
            border-radius: 4px;
            font-size: 1.1rem;
            line-height: 1.8;
            font-family: inherit;
            resize: vertical;
            display: none;
        }

        .text-editor.show {
            display: block;
        }

        .editor-controls {
            margin-bottom: 1rem;
            display: none;
        }

        .editor-controls.show {
            display: flex;
            gap: 1rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .editor-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #3498db;
            background-color: #3498db;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: normal;
        }

        .editor-btn:hover {
            background-color: #2980b9;
        }

        .editor-btn.secondary {
            background-color: #95a5a6;
            border-color: #95a5a6;
        }

        .editor-btn.secondary:hover {
            background-color: #7f8c8d;
        }

        .sidebar {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .dictionary-section {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
            min-height: 200px;
        }

        .dictionary-section h2 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-weight: normal;
        }

        .dictionary-content {
            font-size: 0.95rem;
        }

        .placeholder {
            color: #999;
            font-style: italic;
            font-weight: normal;
        }

        .controls-section {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .controls-section h3 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-weight: normal;
        }

        .control-group {
            margin-bottom: 0.8rem;
        }

        .control-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: normal;
        }

        .control-group input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .bottom-meanings {
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            padding: 1.5rem 2rem;
            display: none;
        }

        .bottom-meanings.show {
            display: block;
        }

        .bottom-meanings h3 {
            font-size: 1.1rem;
            margin-bottom: 1rem;
            color: #2c3e50;
            font-weight: normal;
        }

        .meanings-content {
            font-size: 0.95rem;
            font-weight: normal;
        }

        /* Word styling - colors based on difficulty level */
        .word-level-a {
            color: #9b59b6;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-a:hover {
            background-color: rgba(155, 89, 182, 0.1);
        }

        .word-level-b {
            color: #f39c12;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-b:hover {
            background-color: rgba(243, 156, 18, 0.1);
        }

        .word-level-c {
            color: #e74c3c;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-c:hover {
            background-color: rgba(231, 76, 60, 0.1);
        }

        .word-level-cplus {
            color: #e16711;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-level-cplus:hover {
            background-color: rgba(225, 103, 17, 0.1);
        }

        /* Regular words (sight words) - default black */
        .word-regular {
            color: #333;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .word-regular:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* When level is disabled, show as black */
        .word-level-a.disabled,
        .word-level-b.disabled,
        .word-level-c.disabled,
        .word-level-cplus.disabled {
            color: #333;
        }

        .word-level-a.disabled:hover,
        .word-level-b.disabled:hover,
        .word-level-c.disabled:hover,
        .word-level-cplus.disabled:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* Phrase styling - using underline and colors */
        .phrase-level-a {
            color: #9b59b6;
            text-decoration: underline;
            text-decoration-color: #9b59b6;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-a:hover {
            background-color: rgba(155, 89, 182, 0.1);
        }

        .phrase-level-b {
            color: #f39c12;
            text-decoration: underline;
            text-decoration-color: #f39c12;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-b:hover {
            background-color: rgba(243, 156, 18, 0.1);
        }

        .phrase-level-c {
            color: #e74c3c;
            text-decoration: underline;
            text-decoration-color: #e74c3c;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-c:hover {
            background-color: rgba(231, 76, 60, 0.1);
        }

        .phrase-level-cplus {
            color: #e16711;
            text-decoration: underline;
            text-decoration-color: #e16711;
            cursor: pointer;
            font-weight: normal;
            transition: background-color 0.2s;
        }

        .phrase-level-cplus:hover {
            background-color: rgba(225, 103, 17, 0.1);
        }

        /* When phrase level is disabled */
        .phrase-level-a.disabled,
        .phrase-level-b.disabled,
        .phrase-level-c.disabled,
        .phrase-level-cplus.disabled {
            color: #333;
            text-decoration-color: #333;
        }

        .phrase-level-a.disabled:hover,
        .phrase-level-b.disabled:hover,
        .phrase-level-c.disabled:hover,
        .phrase-level-cplus.disabled:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* Inline meanings */
        .inline-meaning {
            color: #666;
            font-size: 0.9rem;
            font-style: italic;
            margin-left: 0.3rem;
            font-weight: normal;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
                padding: 1rem;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .text-area {
                max-height: 50vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header with level controls -->
        <header class="header">
            <h1>English Learning Tool</h1>
            <div class="level-controls">
                <span>显示颜色:</span>
                <button class="level-btn" data-level="A">A级 (220-1000)</button>
                <button class="level-btn" data-level="B">B级 (1000-2000)</button>
                <button class="level-btn" data-level="C">C级 (2000-3000)</button>
                <button class="level-btn" data-level="C+">C+级 (不常用)</button>
                <button class="edit-btn" id="editBtn">编辑模式</button>
            </div>
        </header>

        <!-- Main content area -->
        <main class="main-content">
            <!-- Left side: Text area -->
            <div class="text-area">
                <div class="editor-controls" id="editorControls">
                    <button class="editor-btn" id="processBtn">处理文本</button>
                    <button class="editor-btn secondary" id="cancelBtn">取消</button>
                    <button class="editor-btn secondary" id="clearBtn">清空</button>
                    <span style="color: #666; font-size: 0.9rem;">粘贴英文文本后点击"处理文本"进行自动分级</span>
                </div>
                <textarea class="text-editor" id="textEditor" placeholder="在这里粘贴或输入英文文本..."></textarea>
                <div class="text-content" id="textContent">
                    <!-- Sample text for demonstration -->
                    <p><span class="word-regular" data-word="The">The</span> <span class="word-level-a" data-word="quick">quick</span> <span class="word-regular" data-word="brown">brown</span> <span class="word-regular" data-word="fox">fox</span> <span class="phrase-level-a" data-phrase="jumps over"><span class="word-level-a" data-word="jumps">jumps</span> <span class="word-regular" data-word="over">over</span></span> <span class="word-regular" data-word="the">the</span> <span class="word-regular" data-word="lazy">lazy</span> <span class="word-regular" data-word="dog">dog</span>. <span class="word-regular" data-word="This">This</span> <span class="word-level-b" data-word="sentence">sentence</span> <span class="word-level-b" data-word="contains">contains</span> <span class="word-level-c" data-word="various">various</span> <span class="word-regular" data-word="words">words</span> <span class="word-regular" data-word="of">of</span> <span class="word-level-a" data-word="different">different</span> <span class="word-level-b" data-word="difficulty">difficulty</span> <span class="word-regular" data-word="levels">levels</span>. <span class="word-regular" data-word="Some">Some</span> <span class="phrase-level-c" data-phrase="fixed expressions"><span class="word-regular" data-word="fixed">fixed</span> <span class="word-level-c" data-word="expressions">expressions</span></span> <span class="word-regular" data-word="are">are</span> <span class="word-regular" data-word="also">also</span> <span class="word-level-b" data-word="included">included</span> <span class="word-regular" data-word="to">to</span> <span class="word-level-cplus" data-word="demonstrate">demonstrate</span> <span class="word-regular" data-word="the">the</span> <span class="word-level-cplus" data-word="functionality">functionality</span>.</p>
                    
                    <p><span class="word-level-a" data-word="Learning">Learning</span> <span class="word-regular" data-word="English">English</span> <span class="word-level-b" data-word="requires">requires</span> <span class="word-level-a" data-word="practice">practice</span> <span class="word-regular" data-word="and">and</span> <span class="word-level-b" data-word="dedication">dedication</span>. <span class="word-level-a" data-word="Students">Students</span> <span class="word-level-a" data-word="often">often</span> <span class="phrase-level-c" data-phrase="struggle with"><span class="word-level-c" data-word="struggle">struggle</span> <span class="word-regular" data-word="with">with</span></span> <span class="word-level-c" data-word="understanding">understanding</span> <span class="word-level-cplus" data-word="complex">complex</span> <span class="word-level-c" data-word="vocabulary">vocabulary</span> <span class="word-regular" data-word="and">and</span> <span class="phrase-level-cplus" data-phrase="grammatical structures"><span class="word-level-cplus" data-word="grammatical">grammatical</span> <span class="word-level-cplus" data-word="structures">structures</span></span>.</p>
                </div>
            </div>

            <!-- Right side: Dictionary and controls -->
            <div class="sidebar">
                <!-- Dictionary section -->
                <div class="dictionary-section">
                    <h2>Dictionary</h2>
                    <div class="dictionary-content" id="dictionaryContent">
                        <p class="placeholder">Click on a word to see its definition</p>
                    </div>
                </div>

                <!-- Phrase section -->
                <div class="dictionary-section">
                    <h2>固定搭配区</h2>
                    <div class="dictionary-content" id="phraseContent">
                        <p class="placeholder">Click on a phrase to see its meaning</p>
                    </div>
                </div>

                <!-- Controls section -->
                <div class="controls-section">
                    <h3>Display Options</h3>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showLevelA" checked>
                            Show Level A words meanings inline
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showLevelB">
                            Show Level B words meanings inline
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showLevelC">
                            Show Level C words meanings inline
                        </label>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="showPhrases">
                            Show phrase meanings inline
                        </label>
                    </div>
                </div>
            </div>
        </main>

        <!-- Bottom section for word meanings -->
        <footer class="bottom-meanings" id="bottomMeanings">
            <h3>Word Meanings</h3>
            <div class="meanings-content" id="meaningsContent">
                <!-- Meanings will be populated by JavaScript -->
            </div>
        </footer>
    </div>

    <script>
        // Extended word frequency database for automatic classification
        const frequencyDatabase = {
            // Sight words (most common 220 words)
            sightWords: new Set([
                'the', 'of', 'and', 'to', 'a', 'in', 'is', 'it', 'you', 'that', 'he', 'was', 'for', 'on', 'are', 'as', 'with', 'his', 'they', 'i', 'at', 'be', 'this', 'have', 'from', 'or', 'one', 'had', 'by', 'word', 'but', 'not', 'what', 'all', 'were', 'we', 'when', 'your', 'can', 'said', 'there', 'each', 'which', 'she', 'do', 'how', 'their', 'if', 'will', 'up', 'other', 'about', 'out', 'many', 'then', 'them', 'these', 'so', 'some', 'her', 'would', 'make', 'like', 'into', 'him', 'has', 'two', 'more', 'go', 'no', 'way', 'could', 'my', 'than', 'first', 'water', 'been', 'call', 'who', 'its', 'now', 'find', 'long', 'down', 'day', 'did', 'get', 'come', 'made', 'may', 'part', 'over', 'new', 'sound', 'take', 'only', 'little', 'work', 'know', 'place', 'year', 'live', 'me', 'back', 'give', 'most', 'very', 'after', 'thing', 'our', 'name', 'good', 'sentence', 'man', 'think', 'say', 'great', 'where', 'help', 'through', 'much', 'before', 'line', 'right', 'too', 'mean', 'old', 'any', 'same', 'tell', 'boy', 'follow', 'came', 'want', 'show', 'also', 'around', 'form', 'three', 'small', 'set', 'put', 'end', 'why', 'again', 'turn', 'here', 'off', 'went', 'number', 'tell', 'men', 'say', 'every', 'found', 'still', 'between', 'should', 'home', 'big', 'air', 'own', 'under', 'read', 'last', 'never', 'us', 'left', 'along', 'while', 'might', 'next', 'below', 'saw', 'something', 'thought', 'both', 'few', 'those', 'always', 'looked', 'large', 'often', 'together', 'asked', 'house', 'world', 'going', 'school', 'important', 'until', 'food', 'keep', 'children', 'feet', 'land', 'side', 'without', 'once', 'animal', 'life', 'enough', 'took', 'sometimes', 'four', 'head', 'above', 'kind', 'began', 'almost', 'page', 'got', 'earth', 'need', 'far', 'hand', 'high', 'mother', 'light', 'country', 'father', 'let', 'night', 'picture', 'being', 'study', 'second', 'soon', 'story', 'since', 'white', 'ever', 'paper', 'hard', 'near', 'better', 'best', 'across', 'during', 'today', 'however', 'sure', 'knew', 'try', 'told', 'young', 'sun', 'whole', 'hear', 'example', 'heard', 'several', 'change', 'answer', 'room', 'sea', 'against', 'top', 'turned', 'learn', 'point', 'city', 'play', 'toward', 'five', 'himself', 'usually', 'money', 'seen', 'car', 'morning', 'body', 'upon', 'family', 'later', 'move', 'face', 'door', 'cut', 'done', 'group', 'true', 'leave', 'color', 'red', 'friend', 'pretty', 'eat', 'front', 'feel', 'fact', 'weeks', 'mind', 'final', 'gave', 'green', 'oh', 'quick', 'develop', 'talk', 'sleep', 'bird', 'warm', 'free', 'minute', 'strong', 'special', 'moon', 'clear', 'tail', 'produce', 'state', 'full', 'hot', 'check', 'object', 'am', 'rule', 'among', 'noun', 'power', 'cannot', 'able', 'six', 'size', 'dark', 'ball', 'material', 'heavy', 'fine', 'pair', 'circle', 'include', 'built'
            ]),

            // A级 words (220-1000)
            aLevel: new Set([
                'people', 'time', 'person', 'way', 'look', 'government', 'company', 'system', 'problem', 'service', 'hand', 'party', 'student', 'lot', 'woman', 'place', 'case', 'week', 'interest', 'world', 'course', 'because', 'public', 'social', 'local', 'getting', 'nothing', 'community', 'business', 'process', 'policy', 'range', 'different', 'political', 'real', 'experience', 'understand', 'level', 'least', 'possible', 'major', 'personal', 'support', 'whether', 'job', 'program', 'believe', 'happen', 'including', 'ask', 'country', 'eye', 'thought', 'tree', 'cross', 'farm', 'start', 'music', 'mark', 'book', 'letter', 'mile', 'river', 'care', 'plain', 'girl', 'usual', 'ready', 'list', 'though', 'talk', 'soon', 'dog', 'direct', 'song', 'measure', 'product', 'black', 'short', 'numeral', 'class', 'wind', 'question', 'complete', 'ship', 'area', 'half', 'rock', 'order', 'fire', 'south', 'piece', 'pass', 'king', 'space', 'hour', 'hundred', 'remember', 'step', 'early', 'hold', 'west', 'ground', 'reach', 'fast', 'verb', 'sing', 'listen', 'table', 'travel', 'less', 'ten', 'simple', 'vowel', 'war', 'lay', 'pattern', 'slow', 'center', 'love', 'serve', 'appear', 'road', 'map', 'rain', 'govern', 'pull', 'cold', 'notice', 'voice', 'unit', 'town', 'certain', 'fly', 'fall', 'lead', 'cry', 'machine', 'note', 'wait', 'plan', 'figure', 'star', 'box', 'field', 'rest', 'correct', 'pound', 'beauty', 'drive', 'stood', 'contain', 'teach', 'ocean', 'behind', 'street', 'inch', 'multiply', 'course', 'stay', 'wheel', 'force', 'blue', 'decide', 'surface', 'deep', 'island', 'foot', 'busy', 'test', 'record', 'boat', 'common', 'gold', 'plane', 'stead', 'dry', 'wonder', 'laugh', 'thousands', 'ago', 'ran', 'game', 'shape', 'equate', 'miss', 'brought', 'heat', 'snow', 'tire', 'bring', 'yes', 'distant', 'fill', 'east', 'paint', 'language', 'practice', 'jumps', 'learning', 'students'
            ]),

            // B级 words (1000-2000)
            bLevel: new Set([
                'difficulty', 'dedication', 'contains', 'included', 'requires', 'education', 'knowledge', 'information', 'development', 'research', 'analysis', 'approach', 'method', 'technique', 'strategy', 'solution', 'decision', 'management', 'organization', 'structure', 'function', 'operation', 'performance', 'quality', 'standard', 'evaluation', 'assessment', 'improvement', 'progress', 'achievement', 'success', 'effective', 'efficient', 'significant', 'necessary', 'essential', 'critical', 'fundamental', 'basic', 'primary', 'secondary', 'advanced', 'simple', 'easy', 'difficult', 'challenging', 'interesting', 'exciting', 'amazing', 'wonderful', 'excellent', 'outstanding', 'remarkable', 'incredible', 'fantastic', 'brilliant', 'creative', 'innovative', 'original', 'unique', 'particular', 'specific', 'general', 'usual', 'normal', 'regular', 'standard', 'typical', 'average', 'ordinary', 'similar', 'numerous', 'several', 'multiple', 'single', 'individual', 'private', 'cultural', 'economic', 'environmental', 'natural', 'physical', 'mental', 'emotional', 'psychological', 'spiritual', 'moral', 'ethical', 'legal', 'medical', 'scientific', 'technical', 'professional', 'academic', 'educational', 'intellectual', 'practical', 'theoretical', 'abstract', 'concrete', 'actual', 'false', 'wrong', 'left', 'up', 'down', 'high', 'low', 'big', 'small', 'large', 'little', 'huge', 'tiny', 'enormous', 'massive', 'giant', 'miniature', 'wide', 'narrow', 'broad', 'thin', 'thick', 'deep', 'shallow', 'long', 'short', 'tall', 'brief', 'quick', 'slow', 'fast', 'rapid', 'swift', 'gradual', 'sudden', 'immediate', 'instant', 'delayed', 'late', 'early', 'recent', 'ancient', 'modern', 'contemporary', 'current', 'present', 'past', 'future', 'temporary', 'permanent', 'constant', 'variable', 'stable', 'unstable', 'steady', 'changing', 'growing', 'developing', 'increasing', 'decreasing', 'rising', 'falling', 'moving', 'static', 'active', 'passive', 'dynamic', 'energetic', 'powerful', 'strong', 'weak', 'gentle', 'harsh', 'rough', 'smooth', 'soft', 'hard', 'flexible', 'rigid', 'solid', 'liquid', 'gas', 'hot', 'cold', 'warm', 'cool', 'dry', 'wet', 'clean', 'dirty', 'fresh', 'old', 'new', 'young', 'mature', 'experienced', 'skilled', 'talented', 'gifted', 'capable', 'competent', 'qualified', 'expert', 'amateur', 'beginner', 'novice', 'intermediate', 'elementary', 'complex', 'complicated', 'sophisticated', 'refined', 'crude', 'polished', 'finished', 'incomplete', 'partial', 'empty', 'filled', 'loaded', 'packed', 'crowded', 'free', 'available', 'occupied', 'reserved', 'booked', 'scheduled', 'planned', 'organized', 'arranged', 'prepared', 'ready', 'finished', 'accomplished', 'achieved', 'successful', 'failed', 'unsuccessful', 'ineffective', 'inefficient', 'productive', 'unproductive', 'useful', 'useless', 'helpful', 'harmful', 'beneficial', 'detrimental', 'positive', 'negative', 'good', 'bad', 'poor', 'superior', 'inferior', 'better', 'worse', 'best', 'worst', 'perfect', 'imperfect', 'ideal', 'realistic', 'possible', 'impossible', 'probable', 'unlikely', 'certain', 'uncertain', 'sure', 'unsure', 'confident', 'doubtful', 'optimistic', 'pessimistic', 'hopeful', 'hopeless', 'happy', 'sad', 'joyful', 'miserable', 'excited', 'bored', 'interested', 'uninterested', 'curious', 'indifferent', 'concerned', 'unconcerned', 'worried', 'relaxed', 'calm', 'nervous', 'anxious', 'peaceful', 'stressful', 'comfortable', 'uncomfortable', 'pleasant', 'unpleasant', 'enjoyable', 'boring', 'entertaining', 'dull', 'amusing', 'serious', 'funny', 'humorous', 'witty', 'clever', 'smart', 'intelligent', 'wise', 'foolish', 'stupid', 'ignorant'
            ]),

            // C级 words (2000-3000)
            cLevel: new Set([
                'understanding', 'various', 'expressions', 'vocabulary', 'struggle', 'analysis', 'comprehensive', 'appropriate', 'particularly', 'specifically', 'generally', 'obviously', 'certainly', 'definitely', 'absolutely', 'completely', 'entirely', 'totally', 'extremely', 'incredibly', 'remarkably', 'especially', 'significantly', 'considerably', 'substantially', 'dramatically', 'effectively', 'efficiently', 'successfully', 'professionally', 'academically', 'scientifically', 'technically', 'theoretically', 'practically', 'realistically', 'ideally', 'potentially', 'possibly', 'probably', 'clearly', 'apparently', 'seemingly', 'supposedly', 'allegedly', 'reportedly', 'presumably', 'undoubtedly', 'unquestionably', 'indisputably', 'inevitably', 'necessarily', 'essentially', 'basically', 'fundamentally', 'primarily', 'mainly', 'chiefly', 'principally', 'largely', 'mostly', 'typically', 'normally', 'commonly', 'frequently', 'regularly', 'consistently', 'constantly', 'continuously', 'perpetually', 'permanently', 'temporarily', 'occasionally', 'sometimes', 'rarely', 'seldom', 'hardly', 'barely', 'scarcely', 'almost', 'nearly', 'approximately', 'roughly', 'about', 'around', 'exactly', 'precisely', 'notably', 'surprisingly', 'interestingly', 'fortunately', 'unfortunately', 'hopefully', 'evidently', 'admittedly', 'frankly', 'honestly', 'seriously', 'literally', 'actually', 'really', 'truly', 'genuinely', 'sincerely', 'positively', 'surely', 'undeniably', 'irrefutably', 'incontrovertibly', 'conclusively', 'decisively', 'definitively', 'finally', 'ultimately', 'eventually', 'gradually', 'slowly', 'quickly', 'rapidly', 'swiftly', 'immediately', 'instantly', 'suddenly', 'abruptly', 'unexpectedly', 'amazingly', 'astonishingly', 'extraordinarily', 'exceptionally', 'unusually', 'radically'
            ]),

            // C+级 words (不常用词)
            cplusLevel: new Set([
                'demonstrate', 'complex', 'functionality', 'grammatical', 'structures', 'sophisticated', 'comprehensive', 'extraordinary', 'phenomenal', 'magnificent', 'spectacular', 'tremendous', 'overwhelming', 'unprecedented', 'revolutionary', 'evolutionary', 'transformational', 'inspirational', 'motivational', 'educational', 'informational', 'organizational', 'institutional', 'constitutional', 'international', 'multinational', 'transnational', 'intercultural', 'multicultural', 'interdisciplinary', 'multidisciplinary', 'technological', 'methodological', 'psychological', 'philosophical', 'theoretical', 'hypothetical', 'experimental', 'observational', 'analytical', 'statistical', 'mathematical', 'computational', 'algorithmic', 'systematic', 'strategic', 'tactical', 'practical', 'logical', 'rational', 'emotional', 'intuitive', 'creative', 'innovative', 'imaginative', 'artistic', 'aesthetic', 'beautiful', 'magnificent', 'gorgeous', 'stunning', 'breathtaking', 'remarkable', 'extraordinary', 'exceptional', 'outstanding', 'excellent', 'superior', 'premium', 'exclusive', 'luxurious', 'elegant', 'sophisticated', 'refined', 'polished', 'professional', 'competent', 'qualified', 'experienced', 'knowledgeable', 'intelligent', 'brilliant', 'genius', 'talented', 'gifted', 'skilled', 'expert', 'masterful', 'accomplished', 'successful', 'prosperous', 'wealthy', 'affluent', 'privileged', 'advantaged', 'fortunate', 'blessed', 'grateful', 'appreciative', 'thankful', 'respectful', 'considerate', 'thoughtful', 'caring', 'compassionate', 'empathetic', 'sympathetic', 'understanding', 'tolerant', 'patient', 'persistent', 'determined', 'dedicated', 'committed', 'devoted', 'loyal', 'faithful', 'trustworthy', 'reliable', 'dependable', 'responsible', 'accountable', 'honest', 'truthful', 'sincere', 'genuine', 'authentic', 'original', 'unique', 'distinctive', 'characteristic', 'typical', 'representative', 'symbolic', 'metaphorical', 'allegorical', 'figurative', 'literal', 'abstract', 'concrete', 'tangible', 'intangible', 'visible', 'invisible', 'apparent', 'obvious', 'evident', 'clear', 'transparent', 'opaque', 'mysterious', 'enigmatic', 'puzzling', 'confusing', 'perplexing', 'bewildering', 'baffling', 'mystifying', 'intriguing', 'fascinating', 'captivating', 'enchanting', 'mesmerizing', 'hypnotizing', 'spellbinding', 'enthralling', 'engaging', 'absorbing', 'compelling', 'convincing', 'persuasive', 'influential', 'powerful', 'impactful', 'significant', 'meaningful', 'purposeful', 'intentional', 'deliberate', 'conscious', 'aware', 'mindful', 'attentive', 'observant', 'perceptive', 'insightful', 'wise', 'knowledgeable', 'informed', 'educated', 'learned', 'scholarly', 'academic', 'intellectual', 'philosophical', 'theoretical', 'conceptual', 'abstract', 'complex', 'complicated', 'intricate', 'elaborate', 'detailed', 'comprehensive', 'thorough', 'complete', 'exhaustive', 'extensive', 'broad', 'wide', 'vast', 'immense', 'enormous', 'huge', 'massive', 'gigantic', 'colossal', 'tremendous', 'incredible', 'unbelievable', 'amazing', 'astonishing', 'astounding', 'stunning', 'shocking', 'surprising', 'unexpected', 'unforeseen', 'unpredictable', 'uncertain', 'ambiguous', 'vague', 'unclear', 'indefinite', 'imprecise', 'approximate', 'rough', 'general', 'broad', 'overall', 'comprehensive', 'inclusive', 'exclusive', 'selective', 'specific', 'particular', 'individual', 'personal', 'private', 'confidential', 'secret', 'hidden', 'concealed', 'disguised', 'masked', 'camouflaged', 'invisible', 'imperceptible', 'undetectable', 'unnoticeable', 'inconspicuous', 'subtle', 'delicate', 'gentle', 'soft', 'tender', 'fragile', 'vulnerable', 'sensitive', 'emotional', 'sentimental', 'romantic', 'passionate', 'intense', 'extreme', 'radical', 'revolutionary', 'innovative', 'creative', 'original', 'unique', 'distinctive', 'special', 'exceptional', 'extraordinary', 'remarkable', 'outstanding', 'excellent', 'superior', 'premium', 'high-quality', 'top-notch', 'first-class', 'world-class', 'international', 'global', 'universal', 'widespread', 'common', 'popular', 'famous', 'well-known', 'renowned', 'celebrated', 'acclaimed', 'praised', 'admired', 'respected', 'honored', 'revered', 'worshipped', 'idolized', 'beloved', 'cherished', 'treasured', 'valued', 'appreciated', 'grateful', 'thankful', 'blessed', 'fortunate', 'lucky', 'successful', 'prosperous', 'wealthy', 'rich', 'affluent', 'well-off', 'comfortable', 'secure', 'safe', 'protected', 'defended', 'guarded', 'shielded', 'covered', 'sheltered', 'hidden', 'concealed', 'secret', 'mysterious', 'enigmatic', 'puzzling', 'confusing', 'perplexing', 'bewildering', 'baffling', 'mystifying', 'intriguing', 'fascinating', 'captivating', 'enchanting', 'mesmerizing', 'hypnotizing', 'spellbinding', 'enthralling', 'engaging', 'absorbing', 'compelling', 'convincing', 'persuasive', 'influential', 'powerful', 'impactful', 'significant', 'meaningful', 'purposeful', 'intentional', 'deliberate', 'conscious', 'aware', 'mindful', 'attentive', 'observant', 'perceptive', 'insightful', 'wise', 'knowledgeable', 'informed', 'educated', 'learned', 'scholarly', 'academic', 'intellectual', 'philosophical', 'theoretical', 'conceptual', 'abstract'
            ])
        };

        // Common phrases database
        const commonPhrases = {
            'in order to': 'B',
            'as well as': 'A',
            'such as': 'A',
            'due to': 'B',
            'because of': 'A',
            'in spite of': 'B',
            'instead of': 'A',
            'rather than': 'B',
            'more than': 'A',
            'less than': 'A',
            'at least': 'A',
            'at most': 'B',
            'for example': 'A',
            'for instance': 'B',
            'in fact': 'A',
            'in general': 'B',
            'in particular': 'B',
            'on the other hand': 'B',
            'as a result': 'B',
            'in conclusion': 'C',
            'struggle with': 'C',
            'deal with': 'A',
            'cope with': 'B',
            'focus on': 'A',
            'depend on': 'A',
            'rely on': 'B',
            'consist of': 'B',
            'result in': 'B',
            'lead to': 'A',
            'contribute to': 'B',
            'according to': 'B',
            'thanks to': 'A',
            'in terms of': 'C',
            'with regard to': 'C',
            'with respect to': 'C',
            'in relation to': 'C',
            'fixed expressions': 'C',
            'jumps over': 'A',
            'grammatical structures': 'C+'
        };

        // Sample word database with meanings
        const wordDatabase = {
            // A级词汇 (220-1000)
            'quick': { level: 'A', meaning: 'moving fast; happening in a short time' },
            'practice': { level: 'A', meaning: 'repeated exercise to improve skill' },
            'jumps': { level: 'A', meaning: 'moves quickly up and over something' },
            'different': { level: 'A', meaning: 'not the same' },
            'Learning': { level: 'A', meaning: 'the process of acquiring knowledge' },
            'Students': { level: 'A', meaning: 'people who are learning' },
            'often': { level: 'A', meaning: 'frequently' },
            
            // B级词汇 (1000-2000)
            'difficulty': { level: 'B', meaning: 'the state of being hard to do or understand' },
            'dedication': { level: 'B', meaning: 'commitment to a task or purpose' },
            'sentence': { level: 'B', meaning: 'a group of words that express a complete thought' },
            'contains': { level: 'B', meaning: 'has or includes something' },
            'included': { level: 'B', meaning: 'contained as part of a whole' },
            'requires': { level: 'B', meaning: 'needs' },
            
            // C级词汇 (2000-3000)
            'various': { level: 'C', meaning: 'different from one another; diverse' },
            'expressions': { level: 'C', meaning: 'words or phrases that convey meaning' },
            'understanding': { level: 'C', meaning: 'the ability to comprehend something' },
            'vocabulary': { level: 'C', meaning: 'all the words used by a language' },
            'struggle': { level: 'C', meaning: 'make great efforts to achieve something difficult' },
            
            // C+级词汇 (不常用词)
            'demonstrate': { level: 'C+', meaning: 'clearly show the existence or truth of something' },
            'complex': { level: 'C+', meaning: 'consisting of many different parts; complicated' },
            'functionality': { level: 'C+', meaning: 'the quality of being functional or practical' },
            'grammatical': { level: 'C+', meaning: 'relating to grammar' },
            'structures': { level: 'C+', meaning: 'arrangements or organizations' },
            
            // Sight words (220个已知词汇，默认黑色)
            'The': { level: 'Sight', meaning: 'used to refer to specific things' },
            'the': { level: 'Sight', meaning: 'used to refer to specific things' },
            'brown': { level: 'Sight', meaning: 'a color like that of earth or wood' },
            'fox': { level: 'Sight', meaning: 'a wild animal with red fur and a bushy tail' },
            'over': { level: 'Sight', meaning: 'above and across' },
            'lazy': { level: 'Sight', meaning: 'unwilling to work or use energy' },
            'dog': { level: 'Sight', meaning: 'a domestic animal kept as a pet' },
            'This': { level: 'Sight', meaning: 'used to identify a specific thing' },
            'of': { level: 'Sight', meaning: 'expressing the relationship between things' },
            'words': { level: 'Sight', meaning: 'units of language that have meaning' },
            'levels': { level: 'Sight', meaning: 'degrees or stages' },
            'Some': { level: 'Sight', meaning: 'a certain amount or number of' },
            'fixed': { level: 'Sight', meaning: 'not able to be changed' },
            'are': { level: 'Sight', meaning: 'present tense of "be"' },
            'also': { level: 'Sight', meaning: 'in addition' },
            'to': { level: 'Sight', meaning: 'expressing direction or intention' },
            'English': { level: 'Sight', meaning: 'the language spoken in England and many other countries' },
            'and': { level: 'Sight', meaning: 'used to connect words or phrases' },
            'with': { level: 'Sight', meaning: 'accompanied by' }
        };

        const phraseDatabase = {
            'jumps over': { level: 'A', meaning: 'leaps above something' },
            'fixed expressions': { level: 'C', meaning: 'phrases with established meanings' },
            'struggle with': { level: 'C', meaning: 'have difficulty dealing with' },
            'grammatical structures': { level: 'C+', meaning: 'patterns of word arrangement in language' }
        };

        // Function to classify word level based on frequency database
        function classifyWord(word) {
            const lowerWord = word.toLowerCase();

            if (frequencyDatabase.sightWords.has(lowerWord)) {
                return 'Sight';
            } else if (frequencyDatabase.aLevel.has(lowerWord)) {
                return 'A';
            } else if (frequencyDatabase.bLevel.has(lowerWord)) {
                return 'B';
            } else if (frequencyDatabase.cLevel.has(lowerWord)) {
                return 'C';
            } else if (frequencyDatabase.cplusLevel.has(lowerWord)) {
                return 'C+';
            } else {
                return 'C+'; // Unknown words default to C+
            }
        }

        // Function to process text and add word classifications
        function processTextContent(text) {
            // Split text into sentences
            const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
            let processedHTML = '';

            sentences.forEach((sentence, index) => {
                if (sentence.trim().length === 0) return;

                // Clean and split sentence into words
                const words = sentence.trim().split(/\s+/);
                let processedSentence = '';

                words.forEach((word, wordIndex) => {
                    // Extract punctuation
                    const match = word.match(/^([^\w]*)(.*?)([^\w]*)$/);
                    const prefix = match[1] || '';
                    const cleanWord = match[2] || '';
                    const suffix = match[3] || '';

                    if (cleanWord.length > 0) {
                        const level = classifyWord(cleanWord);
                        const className = level === 'Sight' ? 'word-regular' : `word-level-${level.toLowerCase()}`;

                        processedSentence += `${prefix}<span class="${className}" data-word="${cleanWord}">${cleanWord}</span>${suffix}`;
                    } else {
                        processedSentence += word;
                    }

                    if (wordIndex < words.length - 1) {
                        processedSentence += ' ';
                    }
                });

                processedHTML += `<p>${processedSentence}.</p>\n`;
            });

            return processedHTML;
        }

        // DOM elements
        const textContent = document.getElementById('textContent');
        const textEditor = document.getElementById('textEditor');
        const editorControls = document.getElementById('editorControls');
        const editBtn = document.getElementById('editBtn');
        const processBtn = document.getElementById('processBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const clearBtn = document.getElementById('clearBtn');
        const dictionaryContent = document.getElementById('dictionaryContent');
        const phraseContent = document.getElementById('phraseContent');
        const bottomMeanings = document.getElementById('bottomMeanings');
        const meaningsContent = document.getElementById('meaningsContent');
        const levelButtons = document.querySelectorAll('.level-btn');
        const checkboxes = {
            A: document.getElementById('showLevelA'),
            B: document.getElementById('showLevelB'),
            C: document.getElementById('showLevelC'),
            phrases: document.getElementById('showPhrases')
        };

        let levelStates = {
            'A': false,
            'B': false,
            'C': false,
            'C+': false
        };

        let isEditMode = false;

        // Edit mode functions
        function toggleEditMode() {
            isEditMode = !isEditMode;

            if (isEditMode) {
                editBtn.classList.add('active');
                editBtn.textContent = '退出编辑';
                textContent.style.display = 'none';
                textEditor.classList.add('show');
                editorControls.classList.add('show');

                // Load current content into editor
                const currentText = textContent.textContent || textContent.innerText || '';
                textEditor.value = currentText.replace(/\s+/g, ' ').trim();
            } else {
                editBtn.classList.remove('active');
                editBtn.textContent = '编辑模式';
                textContent.style.display = 'block';
                textEditor.classList.remove('show');
                editorControls.classList.remove('show');
            }
        }

        function processText() {
            const inputText = textEditor.value.trim();
            if (!inputText) {
                alert('请输入一些文本');
                return;
            }

            // Process the text and generate HTML with word classifications
            const processedHTML = processTextContent(inputText);
            textContent.innerHTML = processedHTML;

            // Exit edit mode
            toggleEditMode();

            // Update display
            updateDisplay();
        }

        function cancelEdit() {
            toggleEditMode();
        }

        function clearEditor() {
            textEditor.value = '';
        }

        // Initialize the application
        function init() {
            addEventListeners();
            updateDisplay();
        }

        function addEventListeners() {
            // Edit mode toggle
            editBtn.addEventListener('click', toggleEditMode);

            // Editor controls
            processBtn.addEventListener('click', processText);
            cancelBtn.addEventListener('click', cancelEdit);
            clearBtn.addEventListener('click', clearEditor);

            // Level button clicks - toggle functionality
            levelButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    if (button === editBtn) return; // Skip edit button

                    const level = button.getAttribute('data-level');
                    levelStates[level] = !levelStates[level];

                    if (levelStates[level]) {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }

                    updateWordColors();
                });
            });

            // Checkbox changes
            Object.values(checkboxes).forEach(checkbox => {
                checkbox.addEventListener('change', updateInlineMeanings);
            });

            // Word and phrase clicks
            document.addEventListener('click', (e) => {
                if (e.target.classList.contains('word-level-a') ||
                    e.target.classList.contains('word-level-b') ||
                    e.target.classList.contains('word-level-c') ||
                    e.target.classList.contains('word-level-cplus') ||
                    e.target.classList.contains('word-regular')) {
                    showWordDefinition(e.target);
                } else if (e.target.classList.contains('phrase-level-a') ||
                           e.target.classList.contains('phrase-level-b') ||
                           e.target.classList.contains('phrase-level-c') ||
                           e.target.classList.contains('phrase-level-cplus')) {
                    showPhraseDefinition(e.target);
                }
            });
        }

        function updateDisplay() {
            updateWordColors();
            updateInlineMeanings();
        }

        function updateWordColors() {
            // Update word colors based on level states
            ['A', 'B', 'C', 'C+'].forEach(level => {
                const selector = `.word-level-${level.toLowerCase()}`;
                const phraseSelector = `.phrase-level-${level.toLowerCase()}`;

                document.querySelectorAll(selector).forEach(el => {
                    if (levelStates[level]) {
                        el.classList.remove('disabled');
                    } else {
                        el.classList.add('disabled');
                    }
                });

                document.querySelectorAll(phraseSelector).forEach(el => {
                    if (levelStates[level]) {
                        el.classList.remove('disabled');
                    } else {
                        el.classList.add('disabled');
                    }
                });
            });
        }

        function updateInlineMeanings() {
            // Remove existing inline meanings
            document.querySelectorAll('.inline-meaning').forEach(el => el.remove());

            // Add inline meanings based on checkboxes
            if (checkboxes.A.checked) {
                addInlineMeaningsForLevel('A');
            }
            if (checkboxes.B.checked) {
                addInlineMeaningsForLevel('B');
            }
            if (checkboxes.C.checked) {
                addInlineMeaningsForLevel('C');
            }
            if (checkboxes.phrases.checked) {
                addInlineMeaningsForPhrases();
            }
        }

        function addInlineMeaningsForLevel(level) {
            const selector = `.word-level-${level.toLowerCase()}`;
            document.querySelectorAll(selector).forEach(wordEl => {
                const word = wordEl.getAttribute('data-word');
                const wordData = wordDatabase[word];
                if (wordData) {
                    const meaningSpan = document.createElement('span');
                    meaningSpan.className = 'inline-meaning';
                    meaningSpan.textContent = `(${wordData.meaning})`;
                    wordEl.parentNode.insertBefore(meaningSpan, wordEl.nextSibling);
                }
            });
        }

        function addInlineMeaningsForPhrases() {
            document.querySelectorAll('[class*="phrase-level-"]').forEach(phraseEl => {
                const phrase = phraseEl.getAttribute('data-phrase');
                const phraseData = phraseDatabase[phrase];
                if (phraseData) {
                    const meaningSpan = document.createElement('span');
                    meaningSpan.className = 'inline-meaning';
                    meaningSpan.textContent = `(${phraseData.meaning})`;
                    phraseEl.parentNode.insertBefore(meaningSpan, phraseEl.nextSibling);
                }
            });
        }

        function showWordDefinition(wordEl) {
            const word = wordEl.getAttribute('data-word');
            let wordData = wordDatabase[word];

            // If word not found in database, create a basic entry based on classification
            if (!wordData) {
                const level = classifyWord(word);
                wordData = {
                    level: level,
                    meaning: 'Definition not available in database'
                };
            }

            let levelDisplay = wordData.level;
            if (wordData.level === 'Sight') {
                levelDisplay = 'Sight Words (已知词汇)';
            } else {
                levelDisplay = `${wordData.level}级`;
            }

            dictionaryContent.innerHTML = `
                <h4>${word}</h4>
                <p><strong>Level:</strong> ${levelDisplay}</p>
                <p><strong>Meaning:</strong> ${wordData.meaning}</p>
            `;

            // Also show in bottom section
            bottomMeanings.classList.add('show');
            meaningsContent.innerHTML = `
                <h4>${word} (${levelDisplay})</h4>
                <p>${wordData.meaning}</p>
            `;
        }

        function showPhraseDefinition(phraseEl) {
            const phrase = phraseEl.getAttribute('data-phrase');
            const phraseData = phraseDatabase[phrase];

            if (phraseData) {
                phraseContent.innerHTML = `
                    <h4>${phrase}</h4>
                    <p><strong>Level:</strong> ${phraseData.level}级</p>
                    <p><strong>Meaning:</strong> ${phraseData.meaning}</p>
                `;

                // Also show in bottom section
                bottomMeanings.classList.add('show');
                meaningsContent.innerHTML = `
                    <h4>固定搭配: ${phrase} (${phraseData.level}级)</h4>
                    <p>${phraseData.meaning}</p>
                `;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>