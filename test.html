<!DOCTYPE html>
<html>
<head>
    <title>Test Vocabulary Classification</title>
</head>
<body>
    <h1>Test Vocabulary Classification</h1>
    <div id="test-results"></div>
    
    <script>
        async function testVocabulary() {
            try {
                const response = await fetch('./vocabulary.json');
                const data = await response.json();
                
                // Create frequency database like in index2.html
                const frequencyDatabase = {
                    aLevel: new Set(data.A1),
                    bLevel: new Set(data.A2),
                    cLevel: new Set(data.A3),
                    cplusLevel: new Set(data.A4)
                };
                
                // Test classification function
                function classifyWord(word) {
                    const lowerWord = word.toLowerCase();
                    if (frequencyDatabase.aLevel.has(lowerWord)) {
                        return 'A';
                    } else if (frequencyDatabase.bLevel.has(lowerWord)) {
                        return 'B';
                    } else if (frequencyDatabase.cLevel.has(lowerWord)) {
                        return 'C';
                    } else if (frequencyDatabase.cplusLevel.has(lowerWord)) {
                        return 'C+';
                    } else {
                        return 'C+'; // Unknown words default to C+
                    }
                }
                
                // Test words
                const testWords = ['common', 'the', 'education', 'demonstrate', 'unknown'];
                let results = '<h2>Test Results:</h2>';
                
                testWords.forEach(word => {
                    const level = classifyWord(word);
                    results += `<p><strong>${word}</strong>: ${level} level</p>`;
                });
                
                // Check if common is in A1
                results += `<h3>Direct Check:</h3>`;
                results += `<p>Is "common" in A1? ${frequencyDatabase.aLevel.has('common')}</p>`;
                results += `<p>A1 size: ${frequencyDatabase.aLevel.size}</p>`;
                results += `<p>A2 size: ${frequencyDatabase.bLevel.size}</p>`;
                results += `<p>A3 size: ${frequencyDatabase.cLevel.size}</p>`;
                results += `<p>A4 size: ${frequencyDatabase.cplusLevel.size}</p>`;
                
                document.getElementById('test-results').innerHTML = results;
                
            } catch (error) {
                document.getElementById('test-results').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        testVocabulary();
    </script>
</body>
</html>
