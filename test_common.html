<!DOCTYPE html>
<html>
<head>
    <title>Test Common Word</title>
    <style>
        .word-level-a { color: #9b59b6; font-weight: bold; }
        .word-level-b { color: #f39c12; font-weight: bold; }
        .word-level-c { color: #e74c3c; font-weight: bold; }
        .word-level-c-plus { color: #e16711; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Test "common" Word Classification</h1>
    <div id="test-text"></div>
    <div id="debug-info"></div>
    
    <script>
        async function testCommon() {
            try {
                const response = await fetch('./vocabulary.json');
                const data = await response.json();
                
                // Create frequency database
                const frequencyDatabase = {
                    aLevel: new Set(data.A1),
                    bLevel: new Set(data.A2),
                    cLevel: new Set(data.A3),
                    cplusLevel: new Set(data.A4)
                };
                
                // Classification function
                function classifyWord(word) {
                    const lowerWord = word.toLowerCase();
                    if (frequencyDatabase.aLevel.has(lowerWord)) {
                        return 'A';
                    } else if (frequencyDatabase.bLevel.has(lowerWord)) {
                        return 'B';
                    } else if (frequencyDatabase.cLevel.has(lowerWord)) {
                        return 'C';
                    } else if (frequencyDatabase.cplusLevel.has(lowerWord)) {
                        return 'C+';
                    } else {
                        return 'C+';
                    }
                }
                
                // Test text with "common"
                const testText = "This is a common word test.";
                const words = testText.split(' ');
                let processedHTML = '';
                
                words.forEach(word => {
                    const cleanWord = word.replace(/[.,!?]/, '');
                    const level = classifyWord(cleanWord);
                    const className = `word-level-${level.toLowerCase().replace('+', '-plus')}`;
                    processedHTML += `<span class="${className}">${word}</span> `;
                });
                
                document.getElementById('test-text').innerHTML = processedHTML;
                
                // Debug info
                let debugInfo = '<h3>Debug Information:</h3>';
                debugInfo += `<p>Is "common" in A1? ${frequencyDatabase.aLevel.has('common')}</p>`;
                debugInfo += `<p>Is "common" in A2? ${frequencyDatabase.bLevel.has('common')}</p>`;
                debugInfo += `<p>Is "common" in A3? ${frequencyDatabase.cLevel.has('common')}</p>`;
                debugInfo += `<p>Is "common" in A4? ${frequencyDatabase.cplusLevel.has('common')}</p>`;
                debugInfo += `<p>Classification of "common": ${classifyWord('common')}</p>`;
                debugInfo += `<p>A1 size: ${frequencyDatabase.aLevel.size}</p>`;
                
                document.getElementById('debug-info').innerHTML = debugInfo;
                
            } catch (error) {
                document.getElementById('debug-info').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        testCommon();
    </script>
</body>
</html>
