<!DOCTYPE html>
<html>
<head>
    <title>Test Unknown Words</title>
    <style>
        .word-level-a { color: #9b59b6; font-weight: bold; }
        .word-level-b { color: #f39c12; font-weight: bold; }
        .word-level-c { color: #e74c3c; font-weight: bold; }
        .word-level-c-plus { color: #e16711; font-weight: bold; }
        .word-regular { color: #333; font-weight: normal; }
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Test Unknown Words Bug Fix</h1>
    
    <div class="test-section">
        <h3>Test Text with Known and Unknown Words:</h3>
        <div id="test-text"></div>
    </div>
    
    <div class="test-section">
        <h3>Classification Results:</h3>
        <div id="classification-results"></div>
    </div>
    
    <script>
        async function testUnknownWords() {
            try {
                const response = await fetch('./vocabulary.json');
                const data = await response.json();
                
                // Create frequency database (matching your updated mapping)
                const frequencyDatabase = {
                    aLevel: new Set(data.A1),          // A1 → A级
                    bLevel: new Set(data.A2),          // A2 → B级
                    cLevel: new Set(data.B1),          // B1 → C级
                    cplusLevel: new Set(data.B2)       // B2 → C+级
                };
                
                // Classification function (matching your fixed version)
                function classifyWord(word) {
                    const lowerWord = word.toLowerCase();
                    if (frequencyDatabase.aLevel.has(lowerWord)) {
                        return 'A';
                    } else if (frequencyDatabase.bLevel.has(lowerWord)) {
                        return 'B';
                    } else if (frequencyDatabase.cLevel.has(lowerWord)) {
                        return 'C';
                    } else if (frequencyDatabase.cplusLevel.has(lowerWord)) {
                        return 'C+';
                    } else {
                        return 'Unknown'; // Fixed: Unknown words return 'Unknown'
                    }
                }
                
                // Test text with mix of known and unknown words
                const testText = "The common word is known, but xyzabc and qwerty are unknown words.";
                const words = testText.split(' ');
                let processedHTML = '';
                let classificationResults = '<ul>';
                
                words.forEach(word => {
                    const cleanWord = word.replace(/[.,!?]/, '');
                    if (cleanWord.length > 0) {
                        const level = classifyWord(cleanWord);
                        const className = level === 'Unknown' ? 'word-regular' : `word-level-${level.toLowerCase().replace('+', '-plus')}`;
                        
                        processedHTML += `<span class="${className}">${word}</span> `;
                        classificationResults += `<li><strong>${cleanWord}</strong>: ${level} → <span class="${className}">Color Preview</span></li>`;
                    } else {
                        processedHTML += `${word} `;
                    }
                });
                
                classificationResults += '</ul>';
                
                document.getElementById('test-text').innerHTML = processedHTML;
                document.getElementById('classification-results').innerHTML = classificationResults;
                
                // Add summary
                const summary = `
                    <h4>Summary:</h4>
                    <p>✅ Known words (common, word, known) should show in colors</p>
                    <p>✅ Unknown words (xyzabc, qwerty) should show in black (word-regular)</p>
                    <p>✅ Unknown words should NOT show in C+ orange color</p>
                `;
                document.getElementById('classification-results').innerHTML += summary;
                
            } catch (error) {
                document.getElementById('classification-results').innerHTML = `<p>Error: ${error.message}</p>`;
            }
        }
        
        testUnknownWords();
    </script>
</body>
</html>
